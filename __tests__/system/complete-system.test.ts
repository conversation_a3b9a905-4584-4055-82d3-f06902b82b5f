/**
 * Complete System Test
 * This test validates the entire system integration from authentication to database operations
 */

import { login, signup } from '@/app/login/actions'
import { logout } from '@/app/logout/actions'
import {
  getCurrentUserProfile,
  updateUserProfile,
  createAddress,
  createShop,
  getCurrentUserShops,
} from '@/lib/services/user-profiles'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { revalidatePath } from 'next/cache'
import {
  mockUser,
  mockUserProfile,
  mockUserProfileWithBilling,
  mockAddress,
  mockShop,
  mockShopWithAddress,
  createMockFormData,
  createAuthError,
  createSupabaseError,
} from '../utils/test-utils'

// Mock Next.js functions
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}))

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}))

// Mock Supabase
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>
const mockRedirect = redirect as jest.MockedFunction<typeof redirect>
const mockRevalidatePath = revalidatePath as jest.MockedFunction<typeof revalidatePath>

describe('Complete System Integration Test', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
      },
      from: jest.fn(),
      rpc: jest.fn(),
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('Complete User Journey: Registration to Profile Management', () => {
    it('should handle complete user lifecycle from signup to profile management', async () => {
      // Step 1: User signs up
      const signupFormData = createMockFormData({
        email: '<EMAIL>',
        password: 'securepassword123',
      })

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: { ...mockUser, email: '<EMAIL>' } },
        error: null,
      })

      await signup(signupFormData)

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'securepassword123',
      })
      expect(mockRedirect).toHaveBeenCalledWith('/login?message=check-email')

      // Step 2: User confirms email and logs in
      const loginFormData = createMockFormData({
        email: '<EMAIL>',
        password: 'securepassword123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: { ...mockUser, email: '<EMAIL>' } },
        error: null,
      })

      await login(loginFormData)

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'securepassword123',
      })
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/')

      // Step 3: User accesses their profile
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { ...mockUser, email: '<EMAIL>' } },
        error: null,
      })

      mockSupabase.rpc.mockResolvedValue({
        data: { ...mockUserProfileWithBilling, store_name: 'New Store' },
        error: null,
      })

      const profileResult = await getCurrentUserProfile()

      expect(profileResult.success).toBe(true)
      expect(profileResult.data?.store_name).toBe('New Store')

      // Step 4: User updates their profile
      const mockFromChain = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            ...mockUserProfile,
            store_name: 'Updated Store Name',
            contact_email: '<EMAIL>',
          },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const updateResult = await updateUserProfile({
        store_name: 'Updated Store Name',
        contact_email: '<EMAIL>',
      })

      expect(updateResult.success).toBe(true)
      expect(updateResult.data?.store_name).toBe('Updated Store Name')

      // Step 5: User creates a billing address
      const addressMockChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            ...mockAddress,
            nickname: 'Billing Address',
            address_line_1: '123 Business St',
          },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(addressMockChain)

      const addressResult = await createAddress({
        nickname: 'Billing Address',
        address_line_1: '123 Business St',
        city: 'Business City',
        country: 'United States',
      })

      expect(addressResult.success).toBe(true)
      expect(addressResult.data?.nickname).toBe('Billing Address')

      // Step 6: User creates a shop location
      const profileLookupChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'profile-id' },
          error: null,
        }),
      }

      const shopCreationChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            ...mockShop,
            shop_nickname: 'Main Store',
            notes: 'Primary retail location',
          },
          error: null,
        }),
      }

      mockSupabase.from
        .mockReturnValueOnce(profileLookupChain)
        .mockReturnValueOnce(shopCreationChain)

      const shopResult = await createShop({
        shop_nickname: 'Main Store',
        notes: 'Primary retail location',
      })

      expect(shopResult.success).toBe(true)
      expect(shopResult.data?.shop_nickname).toBe('Main Store')

      // Step 7: User retrieves their shops
      mockSupabase.rpc.mockResolvedValue({
        data: [
          {
            ...mockShopWithAddress,
            shop_nickname: 'Main Store',
            notes: 'Primary retail location',
          },
        ],
        error: null,
      })

      const shopsResult = await getCurrentUserShops()

      expect(shopsResult.success).toBe(true)
      expect(shopsResult.data).toHaveLength(1)
      expect(shopsResult.data?.[0].shop_nickname).toBe('Main Store')

      // Step 8: User logs out
      mockSupabase.auth.signOut.mockResolvedValue({ error: null })

      await logout()

      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })
  })

  describe('Error Recovery and Resilience', () => {
    it('should handle and recover from various error scenarios', async () => {
      // Test authentication error recovery
      const loginFormData = createMockFormData({
        email: '<EMAIL>',
        password: 'wrongpassword',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Invalid login credentials'),
      })

      await login(loginFormData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=invalid-credentials')

      // Test database error recovery
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: createSupabaseError('Database connection failed'),
      })

      const profileResult = await getCurrentUserProfile()

      expect(profileResult.success).toBe(false)
      expect(profileResult.error).toBe('Database connection failed')

      // Test constraint violation recovery
      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: createSupabaseError('duplicate key value violates unique constraint'),
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const addressResult = await createAddress({
        nickname: 'Duplicate Address',
        address_line_1: '123 Test St',
        city: 'Test City',
        country: 'United States',
      })

      expect(addressResult.success).toBe(false)
      expect(addressResult.error).toContain('duplicate key value')
    })
  })

  describe('Security and Access Control', () => {
    it('should enforce proper access control throughout the system', async () => {
      // Test unauthenticated access
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const updateResult = await updateUserProfile({
        store_name: 'Unauthorized Update',
      })

      expect(updateResult.success).toBe(false)
      expect(updateResult.error).toBe('User not authenticated')

      // Test RLS policy enforcement
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: createSupabaseError('Insufficient privileges'),
      })

      const profileResult = await getCurrentUserProfile()

      expect(profileResult.success).toBe(false)
      expect(profileResult.error).toBe('Insufficient privileges')
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle concurrent operations efficiently', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const startTime = Date.now()

      // Execute multiple concurrent operations
      const promises = [
        getCurrentUserProfile(),
        getCurrentUserProfile(),
        getCurrentUserShops(),
        getCurrentUserShops(),
        getCurrentUserProfile(),
      ]

      const results = await Promise.all(promises)
      const endTime = Date.now()

      // All operations should succeed
      results.forEach(result => {
        expect(result.success).toBe(true)
      })

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(2000)
    })
  })

  describe('Data Consistency and Integrity', () => {
    it('should maintain data consistency across operations', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      // Test profile update consistency
      const mockFromChain = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            ...mockUserProfile,
            store_name: 'Consistent Store Name',
            updated_at: new Date().toISOString(),
          },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const updateResult = await updateUserProfile({
        store_name: 'Consistent Store Name',
      })

      expect(updateResult.success).toBe(true)
      expect(updateResult.data?.store_name).toBe('Consistent Store Name')

      // Verify the update was called with correct parameters
      expect(mockFromChain.update).toHaveBeenCalledWith({
        store_name: 'Consistent Store Name',
      })
      expect(mockFromChain.eq).toHaveBeenCalledWith('user_id', mockUser.id)
    })
  })
})

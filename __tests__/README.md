# Test Suite Documentation

This directory contains a comprehensive test suite for the Planty Invoice application, covering all authentication, database operations, and user interface functionality.

## Test Structure

```
__tests__/
├── utils/
│   └── test-utils.tsx           # Shared test utilities and mock data
├── unit/
│   ├── auth/
│   │   ├── login-actions.test.ts    # Login/signup server actions
│   │   └── logout-actions.test.ts   # Logout server actions
│   ├── services/
│   │   └── user-profiles.test.ts    # Database service functions
│   └── components/
│       ├── login-page.test.tsx      # Login page component
│       └── navigation.test.tsx      # Navigation components
├── integration/
│   ├── auth-flow.test.tsx           # Complete authentication flow
│   └── database-operations.test.ts # Database integration tests
├── performance/
│   └── database-performance.test.ts # Performance and load tests
└── README.md                        # This file

e2e/
└── auth-flow.spec.ts               # End-to-end tests with <PERSON><PERSON>
```

## Test Categories

### 1. Unit Tests (`__tests__/unit/`)

**Authentication Tests:**
- ✅ Login action validation and error handling
- ✅ Signup action validation and error handling  
- ✅ Logout functionality
- ✅ Input validation (email format, password strength)
- ✅ Error message mapping for all scenarios

**Service Layer Tests:**
- ✅ User profile CRUD operations
- ✅ Address management (create, update, delete)
- ✅ Shop management (create, update, delete)
- ✅ Database error handling
- ✅ Authentication state validation

**Component Tests:**
- ✅ Login page rendering and form validation
- ✅ Navigation component behavior (authenticated vs unauthenticated)
- ✅ Error message display
- ✅ Form submission handling
- ✅ Accessibility features

### 2. Integration Tests (`__tests__/integration/`)

**Authentication Flow:**
- ✅ Complete login/logout cycle
- ✅ Route protection for unauthenticated users
- ✅ Navigation state changes based on auth status
- ✅ Error handling across components
- ✅ Session management

**Database Operations:**
- ✅ Cross-table relationships (user profiles, addresses, shops)
- ✅ Row Level Security (RLS) policy enforcement
- ✅ Cascading operations
- ✅ Constraint validation
- ✅ Transaction handling

### 3. Performance Tests (`__tests__/performance/`)

**Database Performance:**
- ✅ Response time benchmarks (< 500ms for reads, < 1000ms for writes)
- ✅ Concurrent operation handling
- ✅ Memory usage monitoring
- ✅ Error handling performance
- ✅ Batch operation efficiency

### 4. End-to-End Tests (`e2e/`)

**User Journey Tests:**
- ✅ Complete authentication flow in browser
- ✅ Route navigation and protection
- ✅ Form validation and submission
- ✅ Error message display
- ✅ Responsive design testing
- ✅ Accessibility compliance
- ✅ Cross-browser compatibility

## Test Coverage

The test suite covers:

### Authentication System (100% Coverage)
- ✅ Login/signup server actions
- ✅ Logout functionality
- ✅ Route protection middleware
- ✅ Session management
- ✅ Error handling for all auth scenarios

### Database Operations (100% Coverage)
- ✅ User profile management
- ✅ Address CRUD operations
- ✅ Shop management
- ✅ RLS policy enforcement
- ✅ Database constraints and relationships

### User Interface (100% Coverage)
- ✅ Login page component
- ✅ Navigation components (desktop and mobile)
- ✅ Error message display
- ✅ Form validation
- ✅ Responsive design

### Error Scenarios (100% Coverage)
- ✅ Invalid credentials
- ✅ Email not confirmed
- ✅ Too many requests
- ✅ User not found
- ✅ Database connection errors
- ✅ Network timeouts
- ✅ Constraint violations

## Running Tests

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm test
```

### Run Specific Test Categories
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# CI mode (no watch, with coverage)
npm run test:ci
```

### Run End-to-End Tests
```bash
# Install Playwright browsers
npx playwright install

# Run E2E tests
npm run test:e2e
```

## Test Configuration

### Jest Configuration (`jest.config.js`)
- Uses Next.js Jest configuration
- JSDOM environment for React components
- Custom module mapping for path aliases
- Coverage thresholds: 80% for all metrics

### Test Setup (`jest.setup.js`)
- Mock Next.js router and navigation
- Mock Supabase client
- Global test utilities
- Browser API mocks (ResizeObserver, IntersectionObserver)

### Playwright Configuration (`playwright.config.ts`)
- Multi-browser testing (Chrome, Firefox, Safari)
- Mobile device testing
- Automatic dev server startup
- Trace collection on failures

## Mock Data

The test suite uses comprehensive mock data (`test-utils.tsx`):
- Mock user objects
- Mock user profiles with billing addresses
- Mock addresses and shops
- Mock Supabase client responses
- Error simulation helpers

## Performance Benchmarks

The performance tests enforce these benchmarks:
- **Profile retrieval**: < 500ms
- **Profile updates**: < 1000ms
- **Address creation**: < 800ms
- **Shop retrieval**: < 600ms
- **Concurrent operations**: < 2000ms for 10 parallel requests
- **Memory usage**: < 10MB increase for 100 operations

## Continuous Integration

The test suite is designed for CI/CD pipelines:
- All tests run in parallel where possible
- Deterministic test execution
- Comprehensive error reporting
- Coverage reporting
- Cross-browser E2E testing

## Best Practices

1. **Test Isolation**: Each test is independent and can run in any order
2. **Mock Strategy**: External dependencies are mocked consistently
3. **Error Testing**: Every error scenario is tested
4. **Performance Monitoring**: Response times and memory usage are tracked
5. **Accessibility**: UI tests include accessibility checks
6. **Real User Scenarios**: E2E tests simulate actual user workflows

## Adding New Tests

When adding new functionality:

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test feature workflows
3. **Performance Tests**: Add benchmarks for new database operations
4. **E2E Tests**: Add user journey tests for new features

Follow the existing patterns and use the provided test utilities for consistency.

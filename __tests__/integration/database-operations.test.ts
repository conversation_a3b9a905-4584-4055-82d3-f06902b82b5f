/**
 * Integration tests for database operations
 * These tests verify the complete database interaction flow
 */

import {
  getCurrentUserProfile,
  updateUserProfile,
  createAddress,
  updateAddress,
  deleteAddress,
  getCurrentUserShops,
  createShop,
  updateShop,
  deleteShop,
} from '@/lib/services/user-profiles'
import { createClient } from '@/lib/supabase/server'
import {
  mockUser,
  mockUserProfile,
  mockUserProfileWithBilling,
  mockAddress,
  mockShop,
  mockShopWithAddress,
  createSupabaseError,
} from '../utils/test-utils'

// Mock Supabase
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>

describe('Database Operations Integration', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: jest.fn(),
      rpc: jest.fn(),
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('User Profile Operations', () => {
    describe('getCurrentUserProfile', () => {
      it('should successfully retrieve user profile with billing address', async () => {
        mockSupabase.rpc.mockResolvedValue({
          data: mockUserProfileWithBilling,
          error: null,
        })

        const result = await getCurrentUserProfile()

        expect(mockSupabase.rpc).toHaveBeenCalledWith('get_current_user_profile')
        expect(result.success).toBe(true)
        expect(result.data).toEqual(mockUserProfileWithBilling)
        expect(result.data?.billing_address_line_1).toBe(mockUserProfileWithBilling.billing_address_line_1)
      })

      it('should handle database connection errors', async () => {
        const error = createSupabaseError('Connection timeout', 'PGRST301')
        mockSupabase.rpc.mockResolvedValue({
          data: null,
          error,
        })

        const result = await getCurrentUserProfile()

        expect(result.success).toBe(false)
        expect(result.error).toBe('Connection timeout')
      })

      it('should handle RLS policy violations', async () => {
        const error = createSupabaseError('Insufficient privileges', 'PGRST116')
        mockSupabase.rpc.mockResolvedValue({
          data: null,
          error,
        })

        const result = await getCurrentUserProfile()

        expect(result.success).toBe(false)
        expect(result.error).toBe('Insufficient privileges')
      })
    })

    describe('updateUserProfile', () => {
      it('should successfully update user profile with proper data validation', async () => {
        const updateData = {
          store_name: 'Updated Store Name',
          contact_email: '<EMAIL>',
          contact_phone: '******-0123',
        }

        const mockFromChain = {
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { ...mockUserProfile, ...updateData },
            error: null,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await updateUserProfile(updateData)

        expect(mockSupabase.from).toHaveBeenCalledWith('user_profiles')
        expect(mockFromChain.update).toHaveBeenCalledWith(updateData)
        expect(mockFromChain.eq).toHaveBeenCalledWith('user_id', mockUser.id)
        expect(result.success).toBe(true)
        expect(result.data?.store_name).toBe('Updated Store Name')
      })

      it('should handle constraint violations', async () => {
        const error = createSupabaseError('duplicate key value violates unique constraint', '23505')
        const mockFromChain = {
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await updateUserProfile({ store_name: 'Duplicate Name' })

        expect(result.success).toBe(false)
        expect(result.error).toContain('duplicate key value')
      })
    })
  })

  describe('Address Operations', () => {
    describe('createAddress', () => {
      it('should successfully create address with complete data', async () => {
        const addressData = {
          nickname: 'New Office',
          address_line_1: '123 Business Ave',
          address_line_2: 'Suite 200',
          city: 'Business City',
          state_province: 'BC',
          postal_code: '12345',
          country: 'United States',
        }

        const mockFromChain = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { ...mockAddress, ...addressData },
            error: null,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await createAddress(addressData)

        expect(mockSupabase.from).toHaveBeenCalledWith('addresses')
        expect(mockFromChain.insert).toHaveBeenCalledWith(addressData)
        expect(result.success).toBe(true)
        expect(result.data?.nickname).toBe('New Office')
        expect(result.data?.address_line_2).toBe('Suite 200')
      })

      it('should handle missing required fields', async () => {
        const error = createSupabaseError('null value in column "city" violates not-null constraint', '23502')
        const mockFromChain = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await createAddress({
          nickname: 'Incomplete Address',
          address_line_1: '123 Street',
          city: '', // Missing required field
          country: 'United States',
        })

        expect(result.success).toBe(false)
        expect(result.error).toContain('not-null constraint')
      })
    })

    describe('updateAddress and deleteAddress', () => {
      it('should successfully update address', async () => {
        const updateData = { nickname: 'Updated Address Name' }
        const mockFromChain = {
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { ...mockAddress, ...updateData },
            error: null,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await updateAddress('address-id', updateData)

        expect(result.success).toBe(true)
        expect(result.data?.nickname).toBe('Updated Address Name')
      })

      it('should successfully delete address', async () => {
        const mockFromChain = {
          delete: jest.fn().mockReturnThis(),
          eq: jest.fn().mockResolvedValue({ error: null }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await deleteAddress('address-id')

        expect(mockSupabase.from).toHaveBeenCalledWith('addresses')
        expect(mockFromChain.delete).toHaveBeenCalled()
        expect(result.success).toBe(true)
      })

      it('should handle foreign key constraint violations on delete', async () => {
        const error = createSupabaseError('update or delete on table "addresses" violates foreign key constraint', '23503')
        const mockFromChain = {
          delete: jest.fn().mockReturnThis(),
          eq: jest.fn().mockResolvedValue({ error }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await deleteAddress('address-id')

        expect(result.success).toBe(false)
        expect(result.error).toContain('foreign key constraint')
      })
    })
  })

  describe('Shop Operations', () => {
    describe('getCurrentUserShops', () => {
      it('should successfully retrieve user shops with addresses', async () => {
        mockSupabase.rpc.mockResolvedValue({
          data: [mockShopWithAddress],
          error: null,
        })

        const result = await getCurrentUserShops()

        expect(mockSupabase.rpc).toHaveBeenCalledWith('get_current_user_shops')
        expect(result.success).toBe(true)
        expect(result.data).toHaveLength(1)
        expect(result.data?.[0]).toEqual(mockShopWithAddress)
      })

      it('should handle empty shops list', async () => {
        mockSupabase.rpc.mockResolvedValue({
          data: [],
          error: null,
        })

        const result = await getCurrentUserShops()

        expect(result.success).toBe(true)
        expect(result.data).toEqual([])
      })
    })

    describe('createShop', () => {
      it('should successfully create shop with address reference', async () => {
        const shopData = {
          shop_nickname: 'Downtown Location',
          address_id: 'address-123',
          notes: 'Main retail location',
          is_active: true,
        }

        // Mock profile lookup
        const mockProfileChain = {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { id: 'profile-id' },
            error: null,
          }),
        }

        // Mock shop creation
        const mockShopChain = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { ...mockShop, ...shopData },
            error: null,
          }),
        }

        mockSupabase.from
          .mockReturnValueOnce(mockProfileChain)
          .mockReturnValueOnce(mockShopChain)

        const result = await createShop(shopData)

        expect(result.success).toBe(true)
        expect(result.data?.shop_nickname).toBe('Downtown Location')
        expect(result.data?.notes).toBe('Main retail location')
      })

      it('should handle unique constraint violations for shop nicknames', async () => {
        const error = createSupabaseError('duplicate key value violates unique constraint "unique_shop_nickname_per_store"', '23505')

        const mockProfileChain = {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { id: 'profile-id' },
            error: null,
          }),
        }

        const mockShopChain = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }

        mockSupabase.from
          .mockReturnValueOnce(mockProfileChain)
          .mockReturnValueOnce(mockShopChain)

        const result = await createShop({
          shop_nickname: 'Duplicate Name',
        })

        expect(result.success).toBe(false)
        expect(result.error).toContain('unique constraint')
      })
    })

    describe('updateShop and deleteShop', () => {
      it('should successfully update shop', async () => {
        const updateData = {
          shop_nickname: 'Updated Shop Name',
          is_active: false,
          notes: 'Temporarily closed',
        }

        const mockFromChain = {
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { ...mockShop, ...updateData },
            error: null,
          }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await updateShop('shop-id', updateData)

        expect(result.success).toBe(true)
        expect(result.data?.shop_nickname).toBe('Updated Shop Name')
        expect(result.data?.is_active).toBe(false)
      })

      it('should successfully delete shop', async () => {
        const mockFromChain = {
          delete: jest.fn().mockReturnThis(),
          eq: jest.fn().mockResolvedValue({ error: null }),
        }

        mockSupabase.from.mockReturnValue(mockFromChain)

        const result = await deleteShop('shop-id')

        expect(result.success).toBe(true)
      })
    })
  })

  describe('Cross-table Operations', () => {
    it('should handle cascading operations correctly', async () => {
      // Test that deleting a user profile cascades to shops
      // This would be tested with actual database operations
      // For now, we verify the service layer handles the relationships correctly

      const mockFromChain = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ error: null }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await deleteShop('shop-id')

      expect(mockSupabase.from).toHaveBeenCalledWith('shops')
      expect(result.success).toBe(true)
    })

    it('should handle RLS policies across related tables', async () => {
      // Test that RLS policies work correctly across joins
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const result = await getCurrentUserProfile()

      expect(result.success).toBe(true)
      // The RLS policy should ensure only the user's own data is returned
      expect(result.data?.user_id).toBe(mockUserProfileWithBilling.user_id)
    })
  })
})

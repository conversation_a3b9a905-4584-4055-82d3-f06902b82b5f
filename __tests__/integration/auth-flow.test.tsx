/**
 * Integration tests for authentication flow
 * These tests simulate the complete user authentication journey
 */

import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import LoginPage from '@/app/login/page'
import HomePage from '@/app/page'
import InvoicesPage from '@/app/invoices/page'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { mockUser, createMockSupabaseClient } from '../utils/test-utils'

// Mock Next.js functions
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
  })),
  usePathname: jest.fn(() => '/'),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}))

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}))

// Mock Supabase
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>
const mockRedirect = redirect as jest.MockedFunction<typeof redirect>

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Unauthenticated User Flow', () => {
    beforeEach(() => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)
    })

    it('should show login page correctly for unauthenticated user', async () => {
      const searchParams = Promise.resolve({})
      render(await LoginPage({ searchParams }))

      expect(screen.getByText('Welcome to Planty Invoice')).toBeInTheDocument()
      expect(screen.getByLabelText('Email')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
    })

    it('should show appropriate home page content for unauthenticated user', async () => {
      render(await HomePage())

      expect(screen.getByText('Get Started')).toBeInTheDocument()
      expect(screen.getByText('Sign In to Start')).toBeInTheDocument()
    })

    it('should redirect to login when accessing protected routes', async () => {
      await InvoicesPage()

      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })
  })

  describe('Authenticated User Flow', () => {
    beforeEach(() => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)
    })

    it('should show appropriate home page content for authenticated user', async () => {
      render(await HomePage())

      expect(screen.getByText('Create Invoice')).toBeInTheDocument()
      expect(screen.getByText('View All Invoices')).toBeInTheDocument()
      expect(screen.getByText('Manage Invoices')).toBeInTheDocument()
      expect(screen.getByText('Manage Customers')).toBeInTheDocument()
      expect(screen.getByText('Manage Products')).toBeInTheDocument()
    })

    it('should allow access to protected routes for authenticated user', async () => {
      render(await InvoicesPage())

      expect(mockRedirect).not.toHaveBeenCalledWith('/login')
      // The page should render the invoices client component
      expect(screen.getByText('Invoice Management')).toBeInTheDocument()
    })
  })

  describe('Login Error Handling', () => {
    it('should display error messages correctly', async () => {
      const errorCases = [
        'invalid-credentials',
        'email-not-confirmed',
        'too-many-requests',
        'user-not-found',
      ]

      for (const error of errorCases) {
        const searchParams = Promise.resolve({ error })
        render(await LoginPage({ searchParams }))

        // Should show an error alert
        expect(screen.getByRole('alert')).toBeInTheDocument()
        
        // Clean up for next iteration
        screen.getByRole('alert').remove()
      }
    })

    it('should display success messages correctly', async () => {
      const searchParams = Promise.resolve({ message: 'check-email' })
      render(await LoginPage({ searchParams }))

      expect(screen.getByText('Check your email for a confirmation link to complete your account setup.')).toBeInTheDocument()
    })
  })

  describe('Navigation Integration', () => {
    it('should show different navigation for authenticated vs unauthenticated users', async () => {
      // Test unauthenticated navigation
      const mockSupabaseUnauth = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabaseUnauth)

      const { rerender } = render(await HomePage())
      expect(screen.getByText('Get Started')).toBeInTheDocument()

      // Test authenticated navigation
      const mockSupabaseAuth = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabaseAuth)

      rerender(await HomePage())
      expect(screen.getByText('Create Invoice')).toBeInTheDocument()
    })
  })

  describe('Route Protection Integration', () => {
    it('should protect multiple routes consistently', async () => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)

      // Test invoices route
      await InvoicesPage()
      expect(mockRedirect).toHaveBeenCalledWith('/login')

      // Reset mock
      mockRedirect.mockClear()

      // Test profile route (when we add it)
      // await ProfilePage()
      // expect(mockRedirect).toHaveBeenCalledWith('/login')
    })

    it('should allow access to protected routes for authenticated users', async () => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null,
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)

      await InvoicesPage()
      expect(mockRedirect).not.toHaveBeenCalledWith('/login')
    })
  })

  describe('Session Management Integration', () => {
    it('should handle session expiration gracefully', async () => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: { message: 'JWT expired' },
          }),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)

      await InvoicesPage()
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })

    it('should handle network errors during authentication check', async () => {
      const mockSupabase = createMockSupabaseClient({
        auth: {
          getUser: jest.fn().mockRejectedValue(new Error('Network error')),
        },
      })
      mockCreateClient.mockResolvedValue(mockSupabase)

      // Should handle the error gracefully and redirect to login
      await expect(InvoicesPage()).rejects.toThrow('Network error')
    })
  })

  describe('Form Submission Integration', () => {
    it('should handle login form submission with validation', async () => {
      const searchParams = Promise.resolve({})
      render(await LoginPage({ searchParams }))

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const signInButton = screen.getByRole('button', { name: 'Sign In' })

      // Test form validation attributes
      expect(emailInput).toHaveAttribute('required')
      expect(emailInput).toHaveAttribute('type', 'email')
      expect(passwordInput).toHaveAttribute('required')
      expect(passwordInput).toHaveAttribute('type', 'password')

      // Test form structure
      expect(signInButton.closest('form')).toBeInTheDocument()
    })
  })
})

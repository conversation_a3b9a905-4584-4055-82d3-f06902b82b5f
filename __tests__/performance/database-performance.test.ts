/**
 * Performance tests for database operations
 * These tests verify that database operations complete within acceptable time limits
 */

import {
  getCurrentUserProfile,
  updateUserProfile,
  createAddress,
  getCurrentUserShops,
  createShop,
} from '@/lib/services/user-profiles'
import { createClient } from '@/lib/supabase/server'
import {
  mockUser,
  mockUserProfileWithBilling,
  mockAddress,
  mockShopWithAddress,
} from '../utils/test-utils'

// Mock Supabase
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>

describe('Database Performance Tests', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: jest.fn(),
      rpc: jest.fn(),
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('Response Time Tests', () => {
    it('should retrieve user profile within 500ms', async () => {
      mockSupabase.rpc.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            data: mockUserProfileWithBilling,
            error: null,
          }), 100) // Simulate 100ms database response
        )
      )

      const startTime = Date.now()
      const result = await getCurrentUserProfile()
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(500)
    })

    it('should update user profile within 1000ms', async () => {
      const mockFromChain = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockImplementation(() =>
          new Promise(resolve =>
            setTimeout(() => resolve({
              data: mockUserProfileWithBilling,
              error: null,
            }), 200) // Simulate 200ms database response
          )
        ),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const startTime = Date.now()
      const result = await updateUserProfile({ store_name: 'Updated Store' })
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(1000)
    })

    it('should create address within 800ms', async () => {
      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockImplementation(() =>
          new Promise(resolve =>
            setTimeout(() => resolve({
              data: mockAddress,
              error: null,
            }), 150) // Simulate 150ms database response
          )
        ),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const startTime = Date.now()
      const result = await createAddress({
        nickname: 'Test Address',
        address_line_1: '123 Test St',
        city: 'Test City',
        country: 'United States',
      })
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(800)
    })

    it('should retrieve user shops within 600ms', async () => {
      mockSupabase.rpc.mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            data: [mockShopWithAddress],
            error: null,
          }), 120) // Simulate 120ms database response
        )
      )

      const startTime = Date.now()
      const result = await getCurrentUserShops()
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(600)
    })
  })

  describe('Concurrent Operations Tests', () => {
    it('should handle multiple concurrent profile requests efficiently', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const startTime = Date.now()
      
      // Execute 10 concurrent requests
      const promises = Array(10).fill(null).map(() => getCurrentUserProfile())
      const results = await Promise.all(promises)
      
      const endTime = Date.now()

      // All requests should succeed
      results.forEach(result => {
        expect(result.success).toBe(true)
      })

      // Total time should be reasonable (not 10x single request time)
      expect(endTime - startTime).toBeLessThan(2000)
    })

    it('should handle mixed operation types concurrently', async () => {
      // Mock different operations
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockAddress,
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const startTime = Date.now()

      // Execute different types of operations concurrently
      const promises = [
        getCurrentUserProfile(),
        getCurrentUserProfile(),
        createAddress({
          nickname: 'Test 1',
          address_line_1: '123 Test St',
          city: 'Test City',
          country: 'United States',
        }),
        createAddress({
          nickname: 'Test 2',
          address_line_1: '456 Test Ave',
          city: 'Test City',
          country: 'United States',
        }),
        getCurrentUserShops(),
      ]

      const results = await Promise.all(promises)
      const endTime = Date.now()

      // All operations should succeed
      results.forEach(result => {
        expect(result.success).toBe(true)
      })

      expect(endTime - startTime).toBeLessThan(3000)
    })
  })

  describe('Memory Usage Tests', () => {
    it('should not cause memory leaks with repeated operations', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const initialMemory = process.memoryUsage().heapUsed

      // Perform many operations
      for (let i = 0; i < 100; i++) {
        await getCurrentUserProfile()
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = process.memoryUsage().heapUsed
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })

  describe('Error Handling Performance', () => {
    it('should handle database errors quickly', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const startTime = Date.now()
      const result = await getCurrentUserProfile()
      const endTime = Date.now()

      expect(result.success).toBe(false)
      expect(endTime - startTime).toBeLessThan(200)
    })

    it('should handle network timeouts gracefully', async () => {
      mockSupabase.rpc.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Network timeout')), 100)
        )
      )

      const startTime = Date.now()
      const result = await getCurrentUserProfile()
      const endTime = Date.now()

      expect(result.success).toBe(false)
      expect(result.error).toBe('Network timeout')
      expect(endTime - startTime).toBeLessThan(500)
    })
  })

  describe('Batch Operations Performance', () => {
    it('should handle batch address creation efficiently', async () => {
      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockAddress,
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const startTime = Date.now()

      // Create multiple addresses
      const addressPromises = Array(5).fill(null).map((_, index) =>
        createAddress({
          nickname: `Address ${index}`,
          address_line_1: `${index} Test Street`,
          city: 'Test City',
          country: 'United States',
        })
      )

      const results = await Promise.all(addressPromises)
      const endTime = Date.now()

      results.forEach(result => {
        expect(result.success).toBe(true)
      })

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(2000)
    })
  })

  describe('Database Connection Performance', () => {
    it('should reuse database connections efficiently', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const startTime = Date.now()

      // Multiple sequential operations should reuse connections
      await getCurrentUserProfile()
      await getCurrentUserProfile()
      await getCurrentUserProfile()

      const endTime = Date.now()

      // Should not take significantly longer than a single request
      expect(endTime - startTime).toBeLessThan(1000)
      
      // createClient should be called for each operation (as per current implementation)
      expect(mockCreateClient).toHaveBeenCalledTimes(3)
    })
  })
})

import { render, screen, fireEvent } from '../../utils/test-utils'
import { Navigation, MobileNavigation } from '@/components/navigation'
import { logout } from '@/app/logout/actions'
import { mockUser } from '../../utils/test-utils'

// Mock the logout action
jest.mock('@/app/logout/actions', () => ({
  logout: jest.fn(),
}))

// Mock usePathname
const mockUsePathname = jest.fn()
jest.mock('next/navigation', () => ({
  ...jest.requireActual('next/navigation'),
  usePathname: () => mockUsePathname(),
}))

const mockLogout = logout as jest.MockedFunction<typeof logout>

describe('Navigation Components', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUsePathname.mockReturnValue('/')
  })

  describe('Navigation', () => {
    it('should render navigation for unauthenticated user', () => {
      render(<Navigation user={null} />)

      expect(screen.getByText('Planty Invoice')).toBeInTheDocument()
      expect(screen.getByText('Home')).toBeInTheDocument()
      expect(screen.getByText('Invoices')).toBeInTheDocument()
      expect(screen.getByText('Sign In')).toBeInTheDocument()
      expect(screen.queryByText('Sign Out')).not.toBeInTheDocument()
    })

    it('should render navigation for authenticated user', () => {
      render(<Navigation user={mockUser} />)

      expect(screen.getByText('Planty Invoice')).toBeInTheDocument()
      expect(screen.getByText('Home')).toBeInTheDocument()
      expect(screen.getByText('Invoices')).toBeInTheDocument()
      expect(screen.getByText('Profile')).toBeInTheDocument()
      expect(screen.getByText(mockUser.email!)).toBeInTheDocument()
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
      expect(screen.queryByText('Sign In')).not.toBeInTheDocument()
    })

    it('should highlight active navigation item', () => {
      mockUsePathname.mockReturnValue('/invoices')
      render(<Navigation user={mockUser} />)

      const invoicesButton = screen.getByText('Invoices').closest('button')
      expect(invoicesButton).toHaveClass('bg-primary', 'text-primary-foreground')
    })

    it('should handle logout action', () => {
      render(<Navigation user={mockUser} />)

      const signOutButton = screen.getByText('Sign Out')
      fireEvent.click(signOutButton)

      // The form should be submitted, which would call the logout action
      expect(signOutButton.closest('form')).toBeInTheDocument()
    })

    it('should have proper links for unauthenticated user', () => {
      render(<Navigation user={null} />)

      const signInLink = screen.getByText('Sign In').closest('a')
      expect(signInLink).toHaveAttribute('href', '/login')

      const homeLink = screen.getByText('Home').closest('a')
      expect(homeLink).toHaveAttribute('href', '/')

      const invoicesLink = screen.getByText('Invoices').closest('a')
      expect(invoicesLink).toHaveAttribute('href', '/invoices')
    })

    it('should have proper links for authenticated user', () => {
      render(<Navigation user={mockUser} />)

      const homeLink = screen.getByText('Home').closest('a')
      expect(homeLink).toHaveAttribute('href', '/')

      const invoicesLink = screen.getByText('Invoices').closest('a')
      expect(invoicesLink).toHaveAttribute('href', '/invoices')

      const profileLink = screen.getByText('Profile').closest('a')
      expect(profileLink).toHaveAttribute('href', '/profile')
    })

    it('should show theme switcher', () => {
      render(<Navigation user={null} />)
      
      // Theme switcher should be present (assuming it has a recognizable element)
      // This test might need adjustment based on the actual ThemeSwitcher implementation
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })
  })

  describe('MobileNavigation', () => {
    it('should render mobile navigation for unauthenticated user', () => {
      render(<MobileNavigation user={null} />)

      expect(screen.getByText('Home')).toBeInTheDocument()
      expect(screen.getByText('Invoices')).toBeInTheDocument()
      expect(screen.getByText('Customers')).toBeInTheDocument()
      expect(screen.getByText('Products')).toBeInTheDocument()
      expect(screen.getByText('Sign In')).toBeInTheDocument()
      expect(screen.queryByText('Sign Out')).not.toBeInTheDocument()
    })

    it('should render mobile navigation for authenticated user', () => {
      render(<MobileNavigation user={mockUser} />)

      expect(screen.getByText('Home')).toBeInTheDocument()
      expect(screen.getByText('Invoices')).toBeInTheDocument()
      expect(screen.getByText('Profile')).toBeInTheDocument()
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
      expect(screen.queryByText('Sign In')).not.toBeInTheDocument()
      expect(screen.queryByText('Customers')).not.toBeInTheDocument()
      expect(screen.queryByText('Products')).not.toBeInTheDocument()
    })

    it('should highlight active navigation item in mobile', () => {
      mockUsePathname.mockReturnValue('/invoices')
      render(<MobileNavigation user={mockUser} />)

      const invoicesButton = screen.getByText('Invoices').closest('button')
      expect(invoicesButton).toHaveClass('text-primary')
    })

    it('should handle logout in mobile navigation', () => {
      render(<MobileNavigation user={mockUser} />)

      const signOutButton = screen.getByText('Sign Out')
      expect(signOutButton.closest('form')).toBeInTheDocument()
    })

    it('should have proper mobile navigation structure', () => {
      render(<MobileNavigation user={null} />)

      // Check for mobile-specific classes
      const mobileNav = screen.getByRole('navigation')
      expect(mobileNav).toHaveClass('md:hidden')
    })

    it('should show icons in mobile navigation', () => {
      render(<MobileNavigation user={mockUser} />)

      // Each navigation item should have an icon (Lucide icons)
      const homeButton = screen.getByText('Home').closest('button')
      const invoicesButton = screen.getByText('Invoices').closest('button')
      const profileButton = screen.getByText('Profile').closest('button')

      expect(homeButton).toBeInTheDocument()
      expect(invoicesButton).toBeInTheDocument()
      expect(profileButton).toBeInTheDocument()
    })

    it('should handle different navigation sets based on auth state', () => {
      // Test unauthenticated navigation
      const { rerender } = render(<MobileNavigation user={null} />)
      
      expect(screen.getByText('Customers')).toBeInTheDocument()
      expect(screen.getByText('Products')).toBeInTheDocument()
      expect(screen.queryByText('Profile')).not.toBeInTheDocument()

      // Test authenticated navigation
      rerender(<MobileNavigation user={mockUser} />)
      
      expect(screen.queryByText('Customers')).not.toBeInTheDocument()
      expect(screen.queryByText('Products')).not.toBeInTheDocument()
      expect(screen.getByText('Profile')).toBeInTheDocument()
    })
  })

  describe('Navigation Active States', () => {
    it('should handle root path correctly', () => {
      mockUsePathname.mockReturnValue('/')
      render(<Navigation user={mockUser} />)

      const homeButton = screen.getByText('Home').closest('button')
      expect(homeButton).toHaveClass('bg-primary', 'text-primary-foreground')
    })

    it('should handle nested paths correctly', () => {
      mockUsePathname.mockReturnValue('/invoices/create')
      render(<Navigation user={mockUser} />)

      const invoicesButton = screen.getByText('Invoices').closest('button')
      expect(invoicesButton).toHaveClass('bg-primary', 'text-primary-foreground')
    })

    it('should not highlight home for non-root paths', () => {
      mockUsePathname.mockReturnValue('/invoices')
      render(<Navigation user={mockUser} />)

      const homeButton = screen.getByText('Home').closest('button')
      expect(homeButton).not.toHaveClass('bg-primary', 'text-primary-foreground')
    })
  })
})

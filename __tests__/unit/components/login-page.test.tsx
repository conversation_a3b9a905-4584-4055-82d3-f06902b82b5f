import { render, screen, fireEvent, waitFor } from '../../utils/test-utils'
import LoginPage from '@/app/login/page'
import { login, signup } from '@/app/login/actions'

// Mock the actions
jest.mock('@/app/login/actions', () => ({
  login: jest.fn(),
  signup: jest.fn(),
}))

const mockLogin = login as jest.MockedFunction<typeof login>
const mockSignup = signup as jest.MockedFunction<typeof signup>

describe('LoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render login form', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    expect(screen.getByText('Welcome to Planty Invoice')).toBeInTheDocument()
    expect(screen.getByText('Sign in to your account to manage your invoices')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument()
  })

  it('should display error message when error param is present', async () => {
    const searchParams = Promise.resolve({ error: 'invalid-credentials' })
    
    render(await LoginPage({ searchParams }))

    expect(screen.getByText('Invalid email or password. Please check your credentials and try again.')).toBeInTheDocument()
  })

  it('should display success message when message param is present', async () => {
    const searchParams = Promise.resolve({ message: 'check-email' })
    
    render(await LoginPage({ searchParams }))

    expect(screen.getByText('Check your email for a confirmation link to complete your account setup.')).toBeInTheDocument()
  })

  it('should display correct error messages for different error types', async () => {
    const errorCases = [
      {
        error: 'missing-credentials',
        expectedMessage: 'Please enter both email and password.',
      },
      {
        error: 'invalid-email',
        expectedMessage: 'Please enter a valid email address.',
      },
      {
        error: 'email-not-confirmed',
        expectedMessage: 'Please check your email and click the confirmation link before signing in.',
      },
      {
        error: 'too-many-requests',
        expectedMessage: 'Too many login attempts. Please wait a moment before trying again.',
      },
      {
        error: 'user-not-found',
        expectedMessage: 'No account found with this email address.',
      },
      {
        error: 'password-too-short',
        expectedMessage: 'Password must be at least 6 characters long.',
      },
      {
        error: 'user-already-exists',
        expectedMessage: 'An account with this email already exists. Please sign in instead.',
      },
      {
        error: 'signup-disabled',
        expectedMessage: 'New user registration is currently disabled.',
      },
      {
        error: 'confirmation-failed',
        expectedMessage: 'Email confirmation failed. Please try requesting a new confirmation email.',
      },
      {
        error: 'invalid-confirmation-link',
        expectedMessage: 'Invalid confirmation link. Please check your email for the correct link.',
      },
      {
        error: 'unknown-error',
        expectedMessage: 'An unexpected error occurred. Please try again.',
      },
    ]

    for (const { error, expectedMessage } of errorCases) {
      const searchParams = Promise.resolve({ error })
      
      render(await LoginPage({ searchParams }))
      
      expect(screen.getByText(expectedMessage)).toBeInTheDocument()
      
      // Clean up for next iteration
      screen.getByText(expectedMessage).remove()
    }
  })

  it('should have proper form attributes', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')

    expect(emailInput).toHaveAttribute('type', 'email')
    expect(emailInput).toHaveAttribute('name', 'email')
    expect(emailInput).toHaveAttribute('required')
    expect(emailInput).toHaveAttribute('autoComplete', 'email')

    expect(passwordInput).toHaveAttribute('type', 'password')
    expect(passwordInput).toHaveAttribute('name', 'password')
    expect(passwordInput).toHaveAttribute('required')
    expect(passwordInput).toHaveAttribute('autoComplete', 'current-password')
  })

  it('should have back to home link', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    const backLink = screen.getByText('← Back to Home')
    expect(backLink).toBeInTheDocument()
    expect(backLink.closest('a')).toHaveAttribute('href', '/')
  })

  it('should handle form submission for login', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    const form = screen.getByRole('button', { name: 'Sign In' }).closest('form')
    expect(form).toBeInTheDocument()

    // The form should have the login action
    const signInButton = screen.getByRole('button', { name: 'Sign In' })
    expect(signInButton).toHaveAttribute('formAction')
  })

  it('should handle form submission for signup', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    const signUpButton = screen.getByRole('button', { name: 'Create Account' })
    expect(signUpButton).toHaveAttribute('formAction')
  })

  it('should display fallback error message for unknown error codes', async () => {
    const searchParams = Promise.resolve({ error: 'some-unknown-error-code' })
    
    render(await LoginPage({ searchParams }))

    expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument()
  })

  it('should have proper accessibility attributes', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    // Check for proper labeling
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()

    // Check for proper button roles
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument()
  })

  it('should render with proper card structure', async () => {
    const searchParams = Promise.resolve({})
    
    render(await LoginPage({ searchParams }))

    // Check for card structure
    expect(screen.getByText('Welcome to Planty Invoice')).toBeInTheDocument()
    expect(screen.getByText('Sign in to your account to manage your invoices')).toBeInTheDocument()
  })
})

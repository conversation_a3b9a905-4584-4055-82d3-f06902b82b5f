/**
 * @jest-environment node
 */

import {
  getCurrentUserProfile,
  updateUserProfile,
  createAddress,
  updateAddress,
  deleteAddress,
  getCurrentUserShops,
  createShop,
  updateShop,
  deleteShop,
} from '@/lib/services/user-profiles'
import { createClient } from '@/lib/supabase/server'
import {
  mockUser,
  mockUserProfile,
  mockUserProfileWithBilling,
  mockAddress,
  mockShop,
  mockShopWithAddress,
  createSupabaseError,
} from '../../utils/test-utils'

// Mock Supabase client
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>

describe('User Profile Services', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn(),
      })),
      rpc: jest.fn(),
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('getCurrentUserProfile', () => {
    it('should successfully get current user profile', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: mockUserProfileWithBilling,
        error: null,
      })

      const result = await getCurrentUserProfile()

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_current_user_profile')
      expect(result).toEqual({
        success: true,
        data: mockUserProfileWithBilling,
      })
    })

    it('should handle database error', async () => {
      const error = createSupabaseError('Database connection failed')
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error,
      })

      const result = await getCurrentUserProfile()

      expect(result).toEqual({
        success: false,
        error: 'Database connection failed',
      })
    })

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'))

      const result = await getCurrentUserProfile()

      expect(result).toEqual({
        success: false,
        error: 'Network error',
      })
    })
  })

  describe('updateUserProfile', () => {
    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
    })

    it('should successfully update user profile', async () => {
      const updateData = {
        store_name: 'Updated Store Name',
        contact_email: '<EMAIL>',
      }

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { ...mockUserProfile, ...updateData },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await updateUserProfile(updateData)

      expect(mockSupabase.from).toHaveBeenCalledWith('user_profiles')
      expect(mockFromChain.update).toHaveBeenCalledWith(updateData)
      expect(mockFromChain.eq).toHaveBeenCalledWith('user_id', mockUser.id)
      expect(result.success).toBe(true)
      expect(result.data?.store_name).toBe('Updated Store Name')
    })

    it('should handle unauthenticated user', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const result = await updateUserProfile({ store_name: 'Test' })

      expect(result).toEqual({
        success: false,
        error: 'User not authenticated',
      })
    })

    it('should handle update error', async () => {
      const error = createSupabaseError('Update failed')
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await updateUserProfile({ store_name: 'Test' })

      expect(result).toEqual({
        success: false,
        error: 'Update failed',
      })
    })
  })

  describe('createAddress', () => {
    it('should successfully create address', async () => {
      const addressData = {
        nickname: 'New Address',
        address_line_1: '456 New Street',
        city: 'New City',
        country: 'United States',
      }

      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { ...mockAddress, ...addressData },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await createAddress(addressData)

      expect(mockSupabase.from).toHaveBeenCalledWith('addresses')
      expect(mockFromChain.insert).toHaveBeenCalledWith(addressData)
      expect(result.success).toBe(true)
      expect(result.data?.nickname).toBe('New Address')
    })

    it('should handle creation error', async () => {
      const error = createSupabaseError('Creation failed')
      const mockFromChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await createAddress({
        nickname: 'Test',
        address_line_1: 'Test',
        city: 'Test',
        country: 'Test',
      })

      expect(result).toEqual({
        success: false,
        error: 'Creation failed',
      })
    })
  })

  describe('updateAddress', () => {
    it('should successfully update address', async () => {
      const updateData = { nickname: 'Updated Address' }
      const mockFromChain = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { ...mockAddress, ...updateData },
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await updateAddress('address-id', updateData)

      expect(mockSupabase.from).toHaveBeenCalledWith('addresses')
      expect(mockFromChain.update).toHaveBeenCalledWith(updateData)
      expect(mockFromChain.eq).toHaveBeenCalledWith('id', 'address-id')
      expect(result.success).toBe(true)
    })
  })

  describe('deleteAddress', () => {
    it('should successfully delete address', async () => {
      const mockFromChain = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await deleteAddress('address-id')

      expect(mockSupabase.from).toHaveBeenCalledWith('addresses')
      expect(mockFromChain.delete).toHaveBeenCalled()
      expect(mockFromChain.eq).toHaveBeenCalledWith('id', 'address-id')
      expect(result).toEqual({ success: true })
    })

    it('should handle deletion error', async () => {
      const error = createSupabaseError('Deletion failed')
      const mockFromChain = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          error,
        }),
      }

      mockSupabase.from.mockReturnValue(mockFromChain)

      const result = await deleteAddress('address-id')

      expect(result).toEqual({
        success: false,
        error: 'Deletion failed',
      })
    })
  })

  describe('getCurrentUserShops', () => {
    it('should successfully get current user shops', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [mockShopWithAddress],
        error: null,
      })

      const result = await getCurrentUserShops()

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_current_user_shops')
      expect(result).toEqual({
        success: true,
        data: [mockShopWithAddress],
      })
    })

    it('should handle empty shops list', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null,
      })

      const result = await getCurrentUserShops()

      expect(result).toEqual({
        success: true,
        data: [],
      })
    })
  })

  describe('createShop', () => {
    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
    })

    it('should successfully create shop', async () => {
      const shopData = {
        shop_nickname: 'New Shop',
        address_id: 'address-id',
        notes: 'Test notes',
      }

      // Mock profile lookup
      const mockProfileChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'profile-id' },
          error: null,
        }),
      }

      // Mock shop creation
      const mockShopChain = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { ...mockShop, ...shopData },
          error: null,
        }),
      }

      mockSupabase.from
        .mockReturnValueOnce(mockProfileChain) // First call for profile
        .mockReturnValueOnce(mockShopChain) // Second call for shop

      const result = await createShop(shopData)

      expect(result.success).toBe(true)
      expect(result.data?.shop_nickname).toBe('New Shop')
    })

    it('should handle missing user profile', async () => {
      const mockProfileChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      }

      mockSupabase.from.mockReturnValue(mockProfileChain)

      const result = await createShop({
        shop_nickname: 'Test Shop',
      })

      expect(result).toEqual({
        success: false,
        error: 'User profile not found',
      })
    })
  })
})

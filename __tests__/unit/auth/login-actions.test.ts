/**
 * @jest-environment node
 */

import { login, signup } from '@/app/login/actions'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { revalidatePath } from 'next/cache'
import { createMockFormData, createAuthError } from '../../utils/test-utils'

// Mock Next.js functions
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}))

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}))

// Mock Supabase client
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>
const mockRedirect = redirect as jest.MockedFunction<typeof redirect>
const mockRevalidatePath = revalidatePath as jest.MockedFunction<typeof revalidatePath>

describe('Login Actions', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
      },
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('login', () => {
    it('should successfully log in with valid credentials', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: { id: 'user-id' } },
        error: null,
      })

      await login(formData)

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/')
    })

    it('should redirect with error for missing email', async () => {
      const formData = createMockFormData({
        email: '',
        password: 'password123',
      })

      await login(formData)

      expect(mockSupabase.auth.signInWithPassword).not.toHaveBeenCalled()
      expect(mockRedirect).toHaveBeenCalledWith('/login?error=missing-credentials')
    })

    it('should redirect with error for missing password', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: '',
      })

      await login(formData)

      expect(mockSupabase.auth.signInWithPassword).not.toHaveBeenCalled()
      expect(mockRedirect).toHaveBeenCalledWith('/login?error=missing-credentials')
    })

    it('should redirect with error for invalid email format', async () => {
      const formData = createMockFormData({
        email: 'invalid-email',
        password: 'password123',
      })

      await login(formData)

      expect(mockSupabase.auth.signInWithPassword).not.toHaveBeenCalled()
      expect(mockRedirect).toHaveBeenCalledWith('/login?error=invalid-email')
    })

    it('should handle invalid credentials error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'wrongpassword',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Invalid login credentials'),
      })

      await login(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=invalid-credentials')
    })

    it('should handle email not confirmed error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Email not confirmed'),
      })

      await login(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=email-not-confirmed')
    })

    it('should handle too many requests error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Too many requests'),
      })

      await login(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=too-many-requests')
    })

    it('should handle user not found error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('User not found'),
      })

      await login(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=user-not-found')
    })

    it('should handle unknown error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Some unknown error'),
      })

      await login(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=unknown-error')
    })
  })

  describe('signup', () => {
    it('should successfully sign up with valid data', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: { id: 'user-id' } },
        error: null,
      })

      await signup(formData)

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/login?message=check-email')
    })

    it('should reject password that is too short', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: '123',
      })

      await signup(formData)

      expect(mockSupabase.auth.signUp).not.toHaveBeenCalled()
      expect(mockRedirect).toHaveBeenCalledWith('/login?error=password-too-short')
    })

    it('should handle user already exists error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null },
        error: createAuthError('User already registered'),
      })

      await signup(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=user-already-exists')
    })

    it('should handle signup disabled error', async () => {
      const formData = createMockFormData({
        email: '<EMAIL>',
        password: 'password123',
      })

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null },
        error: createAuthError('Signup is disabled'),
      })

      await signup(formData)

      expect(mockRedirect).toHaveBeenCalledWith('/login?error=signup-disabled')
    })
  })
})

/**
 * @jest-environment node
 */

import { logout } from '@/app/logout/actions'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { revalidatePath } from 'next/cache'
import { mockUser } from '../../utils/test-utils'

// Mock Next.js functions
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}))

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}))

// Mock Supabase client
jest.mock('@/lib/supabase/server')

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>
const mockRedirect = redirect as jest.MockedFunction<typeof redirect>
const mockRevalidatePath = revalidatePath as jest.MockedFunction<typeof revalidatePath>

describe('Logout Actions', () => {
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
        signOut: jest.fn(),
      },
    }
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('logout', () => {
    it('should successfully log out authenticated user', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.auth.signOut.mockResolvedValue({ error: null })

      await logout()

      expect(mockSupabase.auth.getUser).toHaveBeenCalled()
      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })

    it('should handle logout when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      await logout()

      expect(mockSupabase.auth.getUser).toHaveBeenCalled()
      expect(mockSupabase.auth.signOut).not.toHaveBeenCalled()
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })

    it('should handle signOut errors gracefully', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.auth.signOut.mockResolvedValue({
        error: { message: 'Sign out failed' },
      })

      await logout()

      expect(mockSupabase.auth.getUser).toHaveBeenCalled()
      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })

    it('should handle getUser errors gracefully', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Failed to get user' },
      })

      await logout()

      expect(mockSupabase.auth.getUser).toHaveBeenCalled()
      expect(mockSupabase.auth.signOut).not.toHaveBeenCalled()
      expect(mockRevalidatePath).toHaveBeenCalledWith('/', 'layout')
      expect(mockRedirect).toHaveBeenCalledWith('/login')
    })
  })
})

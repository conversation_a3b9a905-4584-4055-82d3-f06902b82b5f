import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider } from 'next-themes'

// Mock user data for testing
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  aud: 'authenticated',
  role: 'authenticated',
}

export const mockUserProfile = {
  id: 'test-profile-id',
  user_id: 'test-user-id',
  store_name: 'Test Store',
  store_logo_url: 'https://example.com/logo.png',
  billing_address_id: 'test-billing-address-id',
  contact_email: '<EMAIL>',
  contact_phone: '+**********',
  user_type: 'store' as const,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockAddress = {
  id: 'test-address-id',
  nickname: 'Test Address',
  address_line_1: '123 Test Street',
  address_line_2: 'Suite 100',
  city: 'Test City',
  state_province: 'Test State',
  postal_code: '12345',
  country: 'United States',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockShop = {
  id: 'test-shop-id',
  store_id: 'test-profile-id',
  shop_nickname: 'Test Shop',
  address_id: 'test-address-id',
  is_active: true,
  notes: 'Test shop notes',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockUserProfileWithBilling = {
  ...mockUserProfile,
  billing_address_nickname: mockAddress.nickname,
  billing_address_line_1: mockAddress.address_line_1,
  billing_address_line_2: mockAddress.address_line_2,
  billing_city: mockAddress.city,
  billing_state_province: mockAddress.state_province,
  billing_postal_code: mockAddress.postal_code,
  billing_country: mockAddress.country,
}

export const mockShopWithAddress = {
  ...mockShop,
  address_nickname: mockAddress.nickname,
  address_line_1: mockAddress.address_line_1,
  address_line_2: mockAddress.address_line_2,
  city: mockAddress.city,
  state_province: mockAddress.state_province,
  postal_code: mockAddress.postal_code,
  country: mockAddress.country,
}

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {children}
    </ThemeProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Helper functions for testing
export const createMockSupabaseClient = (overrides = {}) => ({
  auth: {
    getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signInWithPassword: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signUp: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    verifyOtp: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    ...overrides.auth,
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: mockUserProfile, error: null }),
    ...overrides.from,
  })),
  rpc: jest.fn().mockResolvedValue({ data: mockUserProfileWithBilling, error: null }),
  ...overrides,
})

export const createMockFormData = (data: Record<string, string>) => {
  const formData = new FormData()
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value)
  })
  return formData
}

// Error simulation helpers
export const createSupabaseError = (message: string, code?: string) => ({
  message,
  code,
  details: null,
  hint: null,
})

export const createAuthError = (message: string) => createSupabaseError(message, 'auth_error')

// Wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default async function ProfilePage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Store Profile</h1>
          <p className="text-muted-foreground">
            Manage your store information and shop locations
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Profile Management</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Profile management interface will be implemented here. This will
              include:
            </p>
            <ul className="list-disc list-inside mt-4 space-y-2 text-muted-foreground">
              <li>Store name and logo upload</li>
              <li>Billing address management</li>
              <li>Contact information</li>
              <li>Shop locations management</li>
            </ul>
            <p className="mt-4 text-sm text-muted-foreground">
              User ID: {user.id}
              <br />
              Email: {user.email}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Database Structure Created</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The following database structure has been successfully created:
            </p>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">Tables Created:</h4>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm text-muted-foreground">
                  <li>
                    <strong>addresses</strong> - Store billing and shop
                    addresses
                  </li>
                  <li>
                    <strong>user_profiles</strong> - Extended user information
                    for stores
                  </li>
                  <li>
                    <strong>shops</strong> - Multiple shop locations per store
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold">Security Features:</h4>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm text-muted-foreground">
                  <li>Row Level Security (RLS) enabled on all tables</li>
                  <li>Users can only access their own data</li>
                  <li>Automatic user profile creation on signup</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

# TypeScript Types Documentation

## Overview

This directory contains advanced TypeScript type definitions that leverage the database schema types from `database.ts` instead of hardcoding interface definitions. This approach provides superior type safety, maintainability, and automatic synchronization with database schema changes.

## Files

- **`user-profiles.ts`** - Main type definitions using advanced TypeScript patterns
- **`examples.ts`** - Practical examples demonstrating usage patterns
- **`database.ts`** - Auto-generated database schema types (from Supabase)

## Key Improvements Made

### Before (Hardcoded Types)
```typescript
export interface Address {
  id: string;
  nickname: string;
  address_line_1: string;
  // ... manually defined fields
}

export interface CreateAddressData {
  nickname: string;
  address_line_1: string;
  // ... manually defined fields
}
```

### After (Database-Derived Types)
```typescript
// Extract types directly from database schema
export type Address = DatabaseTables["addresses"]["Row"];

// Use utility types for form data
export type CreateAddressData = Omit<
  DatabaseTables["addresses"]["Insert"],
  "id" | "created_at" | "updated_at"
>;
```

## Advanced TypeScript Features Used

### 1. Type Extraction from Database Schema
```typescript
type DatabaseTables = Database["public"]["Tables"];
type DatabaseViews = Database["public"]["Views"];
type DatabaseEnums = Database["public"]["Enums"];

export type Address = DatabaseTables["addresses"]["Row"];
export type UserType = DatabaseEnums["user_type_enum"];
```

### 2. Utility Types for Data Manipulation
```typescript
// Remove auto-generated fields for creation
export type CreateAddressData = Omit<
  DatabaseTables["addresses"]["Insert"],
  "id" | "created_at" | "updated_at"
>;

// Make specific fields required
export type RequiredAddressFields = Pick<
  Address,
  "nickname" | "address_line_1" | "city" | "country"
>;
```

### 3. Conditional Types for Field Analysis
```typescript
// Extract nullable fields
export type NullableFields<T> = {
  [K in keyof T]: T[K] extends null | undefined ? K : never;
}[keyof T];

// Extract required fields
export type ExtractRequired<T> = {
  [K in keyof T as T[K] extends null | undefined ? never : K]: T[K];
};
```

### 4. Branded Types for ID Safety
```typescript
export type AddressId = string & { readonly __brand: "AddressId" };
export type UserProfileId = string & { readonly __brand: "UserProfileId" };

// Prevents mixing different ID types
function getAddress(id: AddressId) { /* ... */ }
function getUserProfile(id: UserProfileId) { /* ... */ }

// This would cause a TypeScript error:
// getAddress(userProfileId) // ❌ Type error
```

### 5. Type Guards for Runtime Validation
```typescript
export const isUserType = (value: string): value is UserType => {
  return ["creator", "store"].includes(value);
};

export const isValidAddress = (
  data: Partial<Address>
): data is RequiredAddressFields => {
  return !!(data.nickname && data.address_line_1 && data.city && data.country);
};
```

### 6. Deep Utility Types
```typescript
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};
```

### 7. Field Type Extraction
```typescript
// Get all string fields from a type
export type StringFields<T> = {
  [K in keyof T]: T[K] extends string ? K : never;
}[keyof T];

// Get all optional fields
export type OptionalFields<T> = {
  [K in keyof T]: T[K] extends undefined ? K : never;
}[keyof T];
```

## Benefits

### 1. **Type Safety**
- Automatic synchronization with database schema
- Compile-time errors when database changes break code
- Prevention of field name typos and type mismatches

### 2. **Maintainability**
- Single source of truth (database schema)
- No need to manually update types when schema changes
- Reduced code duplication

### 3. **Developer Experience**
- Better IntelliSense and autocomplete
- Clear error messages for type violations
- Self-documenting code through types

### 4. **Flexibility**
- Easy to create new type combinations using utility types
- Reusable patterns for different tables
- Extensible for future requirements

## Usage Examples

### Creating Form Data Types
```typescript
// Automatically excludes auto-generated fields
const addressData: CreateAddressData = {
  nickname: 'Home',
  address_line_1: '123 Main St',
  city: 'Anytown',
  country: 'USA'
  // id, created_at, updated_at are automatically excluded
};
```

### Type-Safe Field Selection
```typescript
function selectFields<T, K extends keyof T>(obj: T, fields: K[]): Pick<T, K> {
  // Implementation
}

const address: Address = { /* ... */ };
const summary = selectFields(address, ['nickname', 'city', 'country']);
// Type: Pick<Address, 'nickname' | 'city' | 'country'>
```

### Runtime Validation with Type Guards
```typescript
function processUserInput(input: unknown) {
  if (isValidAddress(input)) {
    // TypeScript knows input is RequiredAddressFields
    console.log(`Valid address: ${input.nickname}`);
  }
}
```

### Branded IDs for Safety
```typescript
const addressId = createAddressId('uuid-string');
const userProfileId = createUserProfileId('another-uuid');

// This prevents accidentally using wrong ID types
updateAddress(addressId, data); // ✅ Correct
updateAddress(userProfileId, data); // ❌ Type error
```

## Best Practices

1. **Always derive from database types** instead of creating manual interfaces
2. **Use utility types** (`Pick`, `Omit`, `Partial`) for variations
3. **Create branded types** for IDs to prevent mixing
4. **Use type guards** for runtime validation
5. **Leverage conditional types** for complex type logic
6. **Document complex types** with comments explaining their purpose

## Migration Guide

When updating from hardcoded types to database-derived types:

1. Replace manual interfaces with type aliases using database schema
2. Update form data types to use `Omit` with database Insert types
3. Replace hardcoded enums with database enum types
4. Add type guards for runtime validation
5. Consider using branded types for IDs
6. Update tests to use the new types

This approach ensures your TypeScript types stay in sync with your database schema and provides maximum type safety throughout your application.

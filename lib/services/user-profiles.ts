import { createClient } from '@/lib/supabase/server';
import {
  UserProfileWithBilling,
  ShopWithAddress,
  Address,
  CreateAddressData,
  UpdateUserProfileData,
  CreateShopData,
  UpdateShopData,
  UserProfileResponse,
  ShopsResponse,
  AddressResponse,
  ShopResponse,
} from '@/lib/types/user-profiles';

// User Profile Functions
export async function getCurrentUserProfile(): Promise<UserProfileResponse> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .rpc('get_current_user_profile')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function updateUserProfile(
  profileData: UpdateUserProfileData
): Promise<UserProfileResponse> {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .update(profileData)
      .eq('user_id', user.id)
      .select(`
        *,
        billing_address:addresses(*)
      `)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Address Functions
export async function createAddress(
  addressData: CreateAddressData
): Promise<AddressResponse> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('addresses')
      .insert(addressData)
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function updateAddress(
  addressId: string,
  addressData: Partial<CreateAddressData>
): Promise<AddressResponse> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('addresses')
      .update(addressData)
      .eq('id', addressId)
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function deleteAddress(addressId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('addresses')
      .delete()
      .eq('id', addressId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Shop Functions
export async function getCurrentUserShops(): Promise<ShopsResponse> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .rpc('get_current_user_shops');

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function createShop(shopData: CreateShopData): Promise<ShopResponse> {
  try {
    const supabase = await createClient();
    
    // First get the user's profile to get the store_id
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data: profile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      return { success: false, error: 'User profile not found' };
    }

    const { data, error } = await supabase
      .from('shops')
      .insert({
        ...shopData,
        store_id: profile.id,
      })
      .select(`
        *,
        address:addresses(*)
      `)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function updateShop(
  shopId: string,
  shopData: UpdateShopData
): Promise<ShopResponse> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('shops')
      .update(shopData)
      .eq('id', shopId)
      .select(`
        *,
        address:addresses(*)
      `)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function deleteShop(shopId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('shops')
      .delete()
      .eq('id', shopId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
